# @generated by Move, please check-in and do not edit manually.

[move]
version = 3
manifest_digest = "AC06FB52CB3AB3ABCCB94BC1F07B0D9E37C4CB878603AAFC9E3D97C2654D70E3"
deps_digest = "060AD7E57DFB13104F21BE5F5C3759D03F0553FC3229247D9A7A6B45F50D03A3"
dependencies = [
  { id = "Sui", name = "Sui" },
  { id = "WAL", name = "WAL" },
  { id = "Walrus", name = "Walrus" },
]

[[move.package]]
id = "MoveStdlib"
source = { git = "https://github.com/MystenLabs/sui.git", rev = "mainnet", subdir = "crates/sui-framework/packages/move-stdlib" }

[[move.package]]
id = "Sui"
source = { git = "https://github.com/MystenLabs/sui.git", rev = "mainnet", subdir = "crates/sui-framework/packages/sui-framework" }

dependencies = [
  { id = "MoveStdlib", name = "MoveStdlib" },
]

[[move.package]]
id = "WAL"
source = { local = "local_wal/wal" }

dependencies = [
  { id = "Sui", name = "Sui" },
]

[[move.package]]
id = "Walrus"
source = { local = "local_wal/walrus" }

dependencies = [
  { id = "Sui", name = "Sui" },
  { id = "WAL", name = "WAL" },
]

[move.toolchain-version]
compiler-version = "1.51.0"
edition = "2024"
flavor = "sui"

[env]

[env.mainnet]
chain-id = "35834a8a"
original-published-id = "0xffa84ca890349b3aa27ccf5dc621478f07343509138ebb8ed74f83ce9a089566"
latest-published-id = "0xffa84ca890349b3aa27ccf5dc621478f07343509138ebb8ed74f83ce9a089566"
published-version = "1"
