/// POC test to verify the bug described in Issue.md
/// Bug: update_validator_rewards comment vs. code mismatch
/// The comment requires pausing claim/stake/unstake before per-validator updates,
/// but the function doesn't verify paused state.
module haedal::hawal_bug_poc_test {
    use haedal::walstaking::{Self};
    use haedal::operate::{Self};
    use haedal::hawal_common::{Self};

    const ADMIN_ADDR: address = @0xAD;

    /// This test demonstrates the core bug: update_validator_rewards doesn't check pause state
    /// even though the comment says operations should be paused during reward updates.
    #[test]
    fun test_update_validator_rewards_ignores_pause_state() {
        let (mut scenario_val, mut staking_object, admin_cap, operate_cap, clock_object) =
            hawal_common::hawal_test_setup(ADMIN_ADDR);

        let (mut nodes, mut runner) = hawal_common::walrus_test_setup(ADMIN_ADDR);
        let excluded_node = nodes.pop_back();

        // Setup active validators
        operate::set_active_validators(&operate_cap, &mut staking_object,
            vector[excluded_node.node_id()]);

        hawal_common::print(b"=== BUG DEMONSTRATION ===");
        hawal_common::print(b"Testing if update_validator_rewards checks pause state");

        // Step 1: Pause all operations as the comment in operate.move suggests
        hawal_common::print(b"Step 1: Pausing all operations");
        operate::toggle_stake(&operate_cap, &mut staking_object, true);
        operate::toggle_unstake(&operate_cap, &mut staking_object, true);
        operate::toggle_claim(&operate_cap, &mut staking_object, true);

        // Verify operations are paused
        assert!(walstaking::query_pause_claim(&staking_object) == true, 1);
        hawal_common::print(b"Confirmed: Operations are paused");

        // Step 2: Call update_validator_rewards while operations are paused
        // According to the comment in operate.move lines 74-77, this should be the correct procedure:
        // "1. pause claim/stake/unstake"
        // "2. call update_validator_rewards for every validator"
        // "3. resume claim/stake/unstake"
        //
        // However, the function doesn't verify that operations are actually paused!
        hawal_common::print(b"Step 2: Calling update_validator_rewards while paused");
        runner.tx!(ADMIN_ADDR, |staking, system, ctx| {
            // BUG: This call succeeds even though operations are paused!
            // The function should check pause state but doesn't
            operate::update_validator_rewards(&operate_cap, &mut staking_object, system, staking,
                excluded_node.node_id(), ctx);
        });

        hawal_common::print(b"=== BUG CONFIRMED ===");
        hawal_common::print(b"update_validator_rewards succeeded while operations were paused!");
        hawal_common::print(b"This violates the documented procedure in the comment.");
        hawal_common::print(b"The function should verify pause state but doesn't.");

        // Cleanup
        hawal_common::walrus_destroy(nodes, excluded_node, runner);
        hawal_common::hawal_test_tear_down(scenario_val, staking_object, admin_cap, operate_cap, clock_object);
    }
}
